import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { auth } from '@/lib/auth';
import { canAccessArticle } from '@/lib/services/article-access-service';
import { createCommentNotification, createCommentReplyNotification } from '@/lib/notifications';
import { processCommentMentions } from '@/lib/mentions';

// Force Node.js runtime for article access service compatibility
export const runtime = 'nodejs';

// GET /api/comments - Get comments for a post
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const postId = searchParams.get('postId');

    if (!postId) {
      return NextResponse.json(
        { error: 'Post ID is required' },
        { status: 400 }
      );
    }

    const comments = await prisma.comment.findMany({
      where: { postId },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return NextResponse.json(comments);
  } catch (error) {
    console.error('Error fetching comments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch comments' },
      { status: 500 }
    );
  }
}

// POST /api/comments - Create a new comment
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'You must be logged in to comment' },
        { status: 401 }
      );
    }

    const { postId, content, parentId } = await request.json();

    if (!postId || !content) {
      return NextResponse.json(
        { error: 'Post ID and content are required' },
        { status: 400 }
      );
    }

    // Check if the post exists
    const post = await prisma.post.findUnique({
      where: { id: postId },
    });

    if (!post) {
      return NextResponse.json(
        { error: 'Post not found' },
        { status: 404 }
      );
    }

    // Check if the user can access this article (required for commenting)
    const canAccess = await canAccessArticle(session.user.id as string, postId);
    if (!canAccess) {
      return NextResponse.json(
        {
          error: 'You have reached your monthly article limit. Please upgrade your subscription to continue commenting.',
          subscriptionRequired: true
        },
        { status: 403 }
      );
    }

    // If this is a reply, check if the parent comment exists
    if (parentId) {
      const parentComment = await prisma.comment.findUnique({
        where: { id: parentId },
      });

      if (!parentComment) {
        return NextResponse.json(
          { error: 'Parent comment not found' },
          { status: 404 }
        );
      }
    }

    // Create the comment
    const comment = await prisma.comment.create({
      data: {
        content,
        postId,
        authorId: session.user.id as string,
        parentId,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    // Increment the comment count on the post
    await prisma.post.update({
      where: { id: postId },
      data: { commentCount: { increment: 1 } },
    });

    // Get post title for notifications
    const postDetails = await prisma.post.findUnique({
      where: { id: postId },
      select: { title: true }
    });

    const postTitle = postDetails?.title || 'a post';

    // Process mentions in the comment
    await processCommentMentions(
      comment.id,
      content,
      session.user.id as string,
      postId,
      postTitle
    );

    // Create appropriate notifications
    if (parentId) {
      // This is a reply to another comment
      await createCommentReplyNotification({
        parentCommentId: parentId,
        replyCommentId: comment.id,
        actionUserId: session.user.id as string,
      });
    } else {
      // This is a top-level comment
      await createCommentNotification({
        postId,
        commentId: comment.id,
        actionUserId: session.user.id as string,
      });
    }

    return NextResponse.json(comment);
  } catch (error) {
    console.error('Error creating comment:', error);
    return NextResponse.json(
      { error: 'Failed to create comment' },
      { status: 500 }
    );
  }
}
