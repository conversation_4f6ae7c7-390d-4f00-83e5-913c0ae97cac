import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { auth } from '@/lib/auth';

// POST /api/posts/bulk-update - Bulk update post status
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'You must be logged in to update posts' },
        { status: 401 }
      );
    }

    const { postIds, status } = await request.json();

    if (!postIds || !Array.isArray(postIds) || postIds.length === 0) {
      return NextResponse.json(
        { error: 'Post IDs are required and must be a non-empty array' },
        { status: 400 }
      );
    }

    if (!status || !['published', 'draft', 'scheduled'].includes(status)) {
      return NextResponse.json(
        { error: 'Valid status is required (published, draft, or scheduled)' },
        { status: 400 }
      );
    }

    const userId = session.user.id;

    // First, verify that all posts exist and belong to the current user
    const posts = await prisma.post.findMany({
      where: {
        id: { in: postIds },
      },
      select: {
        id: true,
        authorId: true,
        title: true,
        status: true,
      },
    });

    // Check if all posts were found
    if (posts.length !== postIds.length) {
      const foundIds = posts.map(p => p.id);
      const missingIds = postIds.filter(id => !foundIds.includes(id));
      return NextResponse.json(
        { error: `Posts not found: ${missingIds.join(', ')}` },
        { status: 404 }
      );
    }

    // Check if user owns all posts
    const unauthorizedPosts = posts.filter(post => post.authorId !== userId);
    if (unauthorizedPosts.length > 0) {
      return NextResponse.json(
        { error: 'You can only update your own posts' },
        { status: 403 }
      );
    }

    // Perform bulk update
    const updateData: any = { status };

    // If publishing posts, set publishedAt timestamp
    if (status === 'published') {
      updateData.publishedAt = new Date();
    }

    const result = await prisma.post.updateMany({
      where: {
        id: { in: postIds },
        authorId: userId, // Extra safety check
      },
      data: updateData,
    });

    console.log(`Bulk updated ${result.count} posts to ${status} for user ${userId}`);

    return NextResponse.json({
      updatedCount: result.count,
      message: `Successfully updated ${result.count} post(s) to ${status}`,
    });

  } catch (error) {
    console.error('Error in bulk update posts:', error);
    return NextResponse.json(
      { error: 'Failed to update posts. Please try again.' },
      { status: 500 }
    );
  }
}
