'use client';

import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useScheduledPostsPoller } from '@/hooks/use-posts';

/**
 * Component that checks for scheduled posts that need to be published
 * This is a client-side solution for environments where cron jobs aren't available
 */
export function ScheduledPostsCheck() {
  const { data: session } = useSession();

  // Use TanStack Query hook for polling (only for authenticated users)
  const { error } = useScheduledPostsPoller(!!session?.user?.id);

  // Handle errors
  useEffect(() => {
    if (error) {
      console.error('Error checking scheduled posts:', error);
    }
  }, [error]);

  // This component doesn't render anything
  return null;
}
