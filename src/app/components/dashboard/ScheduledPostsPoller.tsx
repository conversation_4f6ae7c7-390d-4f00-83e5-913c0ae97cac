'use client';

import { useEffect } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { useScheduledPostsPoller } from '@/hooks/use-posts';

/**
 * Component that polls the scheduled posts endpoint to check for posts that need to be published
 * This is a client-side alternative to Vercel Cron jobs for the hobby tier
 */
export function ScheduledPostsPoller() {
  const router = useRouter();

  // Use TanStack Query hook for polling
  const { data: scheduledPostsData, error } = useScheduledPostsPoller(true);

  // Handle successful polling results
  useEffect(() => {
    if (scheduledPostsData && scheduledPostsData.publishedCount > 0) {
      toast.success(`${scheduledPostsData.publishedCount} scheduled post${scheduledPostsData.publishedCount === 1 ? '' : 's'} published`);

      // Refresh the page to show the updated posts
      router.refresh();
    }
  }, [scheduledPostsData, router]);

  // Handle errors (but don't show toast to avoid annoying the user)
  useEffect(() => {
    if (error) {
      console.error('Error checking scheduled posts:', error);
    }
  }, [error]);

  // This component doesn't render anything visible
  return null;
}
